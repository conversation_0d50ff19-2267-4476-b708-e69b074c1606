<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
		PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
	<!--开始配置MyBatis，配置全局属性-->
	<settings>
		<!--使用jdbc的getGeneratedKeys获取数据库自增主键值-->
		<setting name="useGeneratedKeys" value="true"/>

		<!--使用列别名替换列名   默认：true
        select name as title from table -->
		<setting name="useColumnLabel" value="true" />

		<!--开启驼峰命名转换 Table(create_time) ->Entity(createTime)-->
		<setting name="mapUnderscoreToCamelCase" value="true"/>
	</settings>
</configuration>