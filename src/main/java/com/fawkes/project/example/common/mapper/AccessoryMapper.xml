<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.AccessoryMapper">
    <resultMap id="Accessory" type="com.fawkes.project.example.common.model.Accessory"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="accessory_name" jdbcType="VARCHAR" property="accessoryName"/>
        <result column="accessory_type" jdbcType="VARCHAR" property="accessoryType"/>
        <result column="category_id" jdbcType="BIGINT" property="categoryId"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="accessory_unit" jdbcType="VARCHAR" property="accessoryUnit"/>
        <result column="accessory_spec" jdbcType="VARCHAR" property="accessorySpec"/>
        <result column="accessory_storage" jdbcType="VARCHAR" property="accessoryStorage"/>
        <result column="accessory_remark" jdbcType="VARCHAR" property="accessoryRemark"/>
        <result column="accessory_supplier" jdbcType="VARCHAR" property="accessorySupplier"/>
        <result column="accessory_pic" jdbcType="LONGVARCHAR" property="accessoryPic"/>
        <result column="cur_price" jdbcType="DECIMAL" property="curPrice"/>
    </resultMap>
    <resultMap id="AccessoryVO" type="com.fawkes.project.example.domain.vo.AccessoryVO" extends="Accessory">
        <result column="accessory_code" jdbcType="VARCHAR" property="accessoryCode"/>
    </resultMap>
    <resultMap id="AccessoryInventoryVO" type="com.fawkes.project.example.domain.vo.AccessoryInventoryVO"
               extends="AccessoryVO">
        <result column="total_arrival_quantity" jdbcType="INTEGER" property="totalArrivalQuantity"/>
        <result column="total_not_arrival_quantity" jdbcType="INTEGER" property="totalNotArrivalQuantity"/>
        <result column="totalUseQuantity" jdbcType="INTEGER" property="totalUseQuantity"/>
        <result column="accessoryId" jdbcType="BIGINT" property="accessoryId"/>
        <result column="totalLossQuantity" jdbcType="BIGINT" property="totalLossQuantity"/>
        <result column="categoryName" jdbcType="VARCHAR" property="categoryName"/>
    </resultMap>
    <resultMap id="StatAccessoryOutVO" type="com.fawkes.project.example.domain.vo.StatAccessoryOutVO">
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_time" jdbcType="DATE" property="orderTime"/>
        <result column="accessory_id" jdbcType="BIGINT" property="accessoryId"/>
        <result column="use_accessory_quantity" jdbcType="INTEGER" property="useAccessoryQuantity"/>
        <result column="loss_accessory_quantity" jdbcType="INTEGER" property="lossAccessoryQuantity"/>
    </resultMap>

    <select id="isExistedAccessory" resultType="java.lang.Boolean">
        select count( * ) from accessory where delete_flag = 0 AND category_id = #{accessory.categoryId} AND ref_id =
        #{accessory.refId}
        <if test="accessory.id != null">AND id != #{accessory.id}</if>
    </select>

    <select id="isUsedInAccessoryOrderById" resultType="java.lang.Boolean">
        SELECT count(*)
        FROM accessory_order a
                 LEFT JOIN accessory_order_goods b on a.id = b.order_id and a.delete_flag = 0 and b.delete_flag = 0
        where b.accessory_id = #{accessoryId,jdbcType=BIGINT}
    </select>

    <select id="isUsedInCraftById" resultType="java.lang.Boolean">
        SELECT COUNT(*)
        FROM product_craft_compose a
                 LEFT JOIN product_craft b ON b.id = a.craft_id
        WHERE a.delete_flag = 0
          AND b.delete_flag = 0
          AND a.accessory_id = #{accessoryId,jdbcType=BIGINT}
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="Accessory">
        select a.id,
               a.create_by,
               a.create_date,
               a.update_by,
               a.update_date,
               a.delete_flag,
               CONCAT(b.dict_name, '-', c.dict_name) as accessory_name,
               a.accessory_type,
               a.category_id,
               a.ref_id,
               a.accessory_unit,
               a.accessory_spec,
               a.accessory_storage,
               a.accessory_remark,
               a.accessory_supplier,
               a.cur_price
        from accessory a
                 LEFT JOIN accessory_dict b on a.category_id = b.id
                 LEFT JOIN accessory_dict c on a.ref_id = c.id
        where a.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectAccessoryList" resultMap="AccessoryVO">
        select a.id,
        CONCAT(b.dict_code, '-', c.dict_code) as accessory_code,
        CONCAT(b.dict_name, '-', c.dict_name) as accessory_name,
        a.accessory_type,
        a.category_id,
        a.ref_id,
        a.accessory_unit,
        a.accessory_spec,
        a.accessory_storage,
        a.accessory_remark,
        a.accessory_supplier,
        a.accessory_pic,
        IFNULL(a.cur_price, 0) as cur_price,
        a.update_date
        from accessory a
        LEFT JOIN accessory_dict b on a.category_id = b.id
        LEFT JOIN accessory_dict c on a.ref_id = c.id
        where a.delete_flag != -1
        <if test="accessoryCode != null and accessoryCode != ''">
            and CONCAT(b.dict_code, '-', c.dict_code) LIKE CONCAT('%', #{accessoryCode}, '%')
        </if>
        <if test="accessoryName != null and accessoryName != ''">
            and CONCAT(b.dict_name, '-', c.dict_name) LIKE CONCAT('%', #{accessoryName}, '%')
        </if>
        <if test="accessoryType != null and accessoryType != ''">
            and a.accessory_type LIKE CONCAT('%', #{accessoryType}, '%')
        </if>
        ORDER BY update_date DESC, accessory_code ASC
    </select>

    <select id="selectAccessoryInventoryList" resultMap="AccessoryInventoryVO">
        SELECT
        a.id,
        CONCAT( b.dict_code, '-', c.dict_code ) AS accessory_code,
        CONCAT( b.dict_name, '-', c.dict_name ) AS accessory_name,
        a.accessory_type,
        a.category_id,
        a.ref_id,
        a.accessory_unit,
        IFNULL(a.accessory_spec, '') AS accessory_spec,
        IFNULL(a.accessory_storage, '') AS accessory_storage,
        IFNULL(a.accessory_remark, '') AS accessory_remark,
        IFNULL(a.accessory_supplier, '') AS accessory_supplier,
        IFNULL(a.cur_price, 0) AS cur_price,
        a.update_date,
        a.accessory_pic,
        b.dict_code AS categoryCode,
        b.dict_name AS categoryName,
        c.dict_code AS refCode,
        IFNULL( SUM( d.arrival_quantity ), 0 ) AS total_arrival_quantity,
        CASE WHEN <![CDATA[ SUM( d.purchase_quantity ) < SUM( d.arrival_quantity ) ]]> THEN 0 ELSE IFNULL( SUM(
        d.purchase_quantity ) - SUM( d.arrival_quantity ), 0 ) END AS total_not_arrival_quantity
        FROM
        accessory a
        LEFT JOIN accessory_dict b ON a.category_id = b.id
        LEFT JOIN accessory_dict c ON a.ref_id = c.id
        LEFT JOIN accessory_order_goods d ON d.accessory_id = a.id and d.delete_flag !=-1
        LEFT JOIN accessory_order e on d.order_id = e.id and e.delete_flag != -1
        WHERE a.delete_flag = 0
        <if test="accessoryCode != null and accessoryCode != ''">
            and CONCAT(b.dict_code, '-', c.dict_code) LIKE CONCAT('%', #{accessoryCode}, '%')
        </if>
        <if test="accessoryName != null and accessoryName != ''">
            and CONCAT( b.dict_name, '-', c.dict_name ) LIKE CONCAT('%', #{accessoryName}, '%')
        </if>
        <if test="accessoryType != null and accessoryType != ''">
            and a.accessory_type LIKE CONCAT('%', #{accessoryType}, '%')
        </if>
        <if test="accessorySupplier != null and accessorySupplier != ''">
            and a.accessory_supplier LIKE CONCAT('%', #{accessorySupplier}, '%')
        </if>
        GROUP BY a.id
        ORDER BY update_date DESC, accessory_code ASC
    </select>

    <select id="getAccessoryUseList" resultMap="AccessoryInventoryVO">
        SELECT c.accessory_id  AS accessoryId,
               SUM(c.quantity) AS totalUseQuantity
        FROM product_order a
                 LEFT JOIN product_object b ON a.id = b.order_id
                 LEFT JOIN product_craft_compose c ON b.craft_id = c.craft_id and c.delete_flag = 0
        WHERE a.delete_flag = 0
          and b.delete_flag = 0
          and c.delete_flag = 0
        GROUP BY c.accessory_id
    </select>

    <select id="getAccessoryLossList" resultMap="AccessoryInventoryVO">
        SELECT b.accessory_id       AS accessoryId,
               SUM(b.loss_quantity) AS totalLossQuantity
        FROM product_order a
                 LEFT JOIN product_order_loss b ON a.id = b.order_id AND b.delete_flag = 0
        WHERE a.delete_flag = 0
          AND b.accessory_id IS NOT NULL
        GROUP BY b.accessory_id
    </select>

    <select id="statAccessoryOutUseByAccessoryId" resultMap="StatAccessoryOutVO">
        SELECT a.id            as order_id,
               a.order_no,
               a.order_time,
               c.accessory_id,
               SUM(c.quantity) AS use_accessory_quantity
        FROM product_order a
                 LEFT JOIN product_object b ON b.order_id = a.id
                 LEFT JOIN product_craft_compose c ON c.craft_id = b.craft_id AND c.delete_flag = 0
        WHERE a.delete_flag = 0
          AND b.delete_flag = 0
          AND c.accessory_id = #{accessoryId}
        GROUP BY a.id
        ORDER BY a.order_time DESC
    </select>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.Accessory">
        insert into accessory (id, create_by, create_date,
                               update_by, update_date, delete_flag,
                               accessory_type, category_id,
                               ref_id, accessory_unit, accessory_spec,
                               accessory_storage, accessory_remark, accessory_supplier,
                               cur_price, accessory_pic)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                #{accessoryType,jdbcType=VARCHAR}, #{categoryId,jdbcType=BIGINT},
                #{refId,jdbcType=BIGINT}, #{accessoryUnit,jdbcType=VARCHAR}, #{accessorySpec,jdbcType=VARCHAR},
                #{accessoryStorage,jdbcType=VARCHAR}, #{accessoryRemark,jdbcType=VARCHAR},
                #{accessorySupplier,jdbcType=VARCHAR},
                #{curPrice,jdbcType=DECIMAL}, #{accessoryPic,jdbcType=LONGVARCHAR})
    </insert>

    <insert id="insertBatch" parameterType="com.fawkes.project.example.common.model.Accessory">
        insert into accessory (id, create_by, create_date,
        update_by, update_date, delete_flag,
        accessory_type, category_id,
        ref_id, accessory_unit, accessory_spec,
        accessory_storage, accessory_remark, accessory_supplier,
        cur_price, accessory_pic)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP},
            #{item.deleteFlag,jdbcType=INTEGER},
            #{item.accessoryType,jdbcType=VARCHAR}, #{item.categoryId,jdbcType=BIGINT},
            #{item.refId,jdbcType=BIGINT}, #{item.accessoryUnit,jdbcType=VARCHAR},
            #{item.accessorySpec,jdbcType=VARCHAR},
            #{item.accessoryStorage,jdbcType=VARCHAR}, #{item.accessoryRemark,jdbcType=VARCHAR},
            #{item.accessorySupplier,jdbcType=VARCHAR},
            #{item.curPrice,jdbcType=DECIMAL}, #{item.accessoryPic,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>

    <update id="deleteById">
        update accessory
        set update_by   = #{updateInfo.updateBy,jdbcType=VARCHAR},
            update_date = #{updateInfo.updateDate,jdbcType=TIMESTAMP},
            delete_flag = -1
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.fawkes.project.example.common.model.Accessory">
        update accessory
        set update_by          = #{updateBy,jdbcType=VARCHAR},
            update_date        = #{updateDate,jdbcType=TIMESTAMP},
            accessory_type     = #{accessoryType,jdbcType=VARCHAR},
            accessory_unit     = #{accessoryUnit,jdbcType=VARCHAR},
            accessory_spec     = #{accessorySpec,jdbcType=VARCHAR},
            accessory_storage  = #{accessoryStorage,jdbcType=VARCHAR},
            accessory_remark   = #{accessoryRemark,jdbcType=VARCHAR},
            accessory_supplier = #{accessorySupplier,jdbcType=VARCHAR},
            accessory_pic      = #{accessoryPic}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="batchUpdateCurPrice" parameterType="com.fawkes.project.example.domain.dto.AccessoryPriceDTO">
        <foreach collection="list" item="item" separator=";">
            update accessory
            set cur_price = #{item.curPrice,jdbcType=DECIMAL}
            where id = #{item.accessoryId,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>