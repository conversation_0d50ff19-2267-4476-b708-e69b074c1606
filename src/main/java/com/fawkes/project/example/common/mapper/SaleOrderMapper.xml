<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fawkes.project.example.common.mapper.SaleOrderMapper">
    <resultMap id="SaleOrder" type="com.fawkes.project.example.common.model.SaleOrder"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_name" jdbcType="VARCHAR" property="orderName"/>
        <result column="sign_date" jdbcType="DATE" property="signDate"/>
        <result column="planned_delivery_date" jdbcType="DATE" property="plannedDeliveryDate"/>
        <result column="client_id" jdbcType="BIGINT" property="clientId"/>
        <result column="liable_user_id" jdbcType="BIGINT" property="liableUserId"/>
        <result column="liable_user_name" jdbcType="VARCHAR" property="liableUserName"/>
        <result column="industry" jdbcType="VARCHAR" property="industry"/>
        <result column="project_type" jdbcType="VARCHAR" property="projectType"/>
        <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="order_enclosure" jdbcType="LONGVARCHAR" property="orderEnclosure"/>
    </resultMap>
    <resultMap id="SaleOrderReturn" type="com.fawkes.project.example.common.model.SaleOrderReturn"
               extends="com.fawkes.project.example.common.mapper.BaseMapper.BaseEntity">
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="obj_id" jdbcType="BIGINT" property="objId"/>
        <result column="return_date" jdbcType="DATE" property="returnDate"/>
        <result column="return_type" jdbcType="VARCHAR" property="returnType"/>
        <result column="return_status" jdbcType="VARCHAR" property="returnStatus"/>
        <result column="return_reason" jdbcType="VARCHAR" property="returnReason"/>
        <result column="return_pic" jdbcType="LONGVARCHAR" property="returnPic"/>
        <result column="return_new_obj_id" jdbcType="BIGINT" property="returnNewObjId"/>
    </resultMap>
    <resultMap id="SaleOrderDTO" type="com.fawkes.project.example.domain.dto.SaleOrderDTO" extends="SaleOrder">
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
    </resultMap>
    <resultMap id="SaleOrderVO" type="com.fawkes.project.example.domain.vo.SaleOrderVO">
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_name" jdbcType="VARCHAR" property="orderName"/>
        <result column="sign_date" jdbcType="DATE" property="signDate"/>
        <result column="planned_delivery_date" jdbcType="DATE" property="plannedDeliveryDate"/>
        <result column="liable_user_name" jdbcType="VARCHAR" property="liableUserName"/>
        <result column="liable_user_id" jdbcType="BIGINT" property="liableUserId"/>
        <result column="industry" jdbcType="VARCHAR" property="industry"/>
        <result column="project_type" jdbcType="VARCHAR" property="projectType"/>
        <result column="client_type" jdbcType="VARCHAR" property="clientType"/>
        <result column="client_name" jdbcType="VARCHAR" property="clientName"/>
        <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount"/>
        <result column="sale_price" jdbcType="DECIMAL" property="salePrice"/>
        <result column="sale_quantity" jdbcType="INTEGER" property="saleQuantity"/>
        <result column="planned_delivery_date" jdbcType="DATE" property="plannedDeliveryDate"/>
        <result column="actual_delivery_date" jdbcType="DATE" property="actualDeliveryDate"/>
        <result column="out_quantity" jdbcType="INTEGER" property="outQuantity"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="client_id" jdbcType="BIGINT" property="clientId"/>
        <result column="order_enclosure" jdbcType="VARCHAR" property="orderEnclosure"/>
        <result column="client_province" jdbcType="VARCHAR" property="clientProvince"/>
    </resultMap>
    <resultMap id="SaleOutGoodsVO" type="com.fawkes.project.example.domain.vo.SaleGoodsVO">
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_model" jdbcType="VARCHAR" property="productModel"/>
        <result column="craft_id" jdbcType="BIGINT" property="craftId"/>
        <result column="craft_code" jdbcType="VARCHAR" property="craftCode"/>
    </resultMap>
    <resultMap id="SaleCraftDetailVO" type="com.fawkes.project.example.domain.vo.SaleOutCraftDetailVO">
        <result column="craft_id" jdbcType="BIGINT" property="craftId"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_model" jdbcType="VARCHAR" property="productModel"/>
        <result column="required_quantity" jdbcType="VARCHAR" property="requiredQuantity"/>
    </resultMap>
    <resultMap id="SaleProductObjVO" type="com.fawkes.project.example.domain.vo.SaleProductObjVO">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="craft_id" jdbcType="BIGINT" property="craftId"/>
        <result column="craft_code" jdbcType="VARCHAR" property="craftCode"/>
        <result column="object_no" jdbcType="VARCHAR" property="objectNo"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <!--  订单信息  -->
        <result column="use_order_id" jdbcType="BIGINT" property="useOrderId"/>
        <result column="sale_order_id" jdbcType="BIGINT" property="saleOrderId"/>
        <!--  产品出库  -->
        <result column="out_date" jdbcType="DATE" property="outDate"/>
        <result column="out_user_id" jdbcType="BIGINT" property="outUserId"/>
        <result column="out_user_name" jdbcType="VARCHAR" property="outUserName"/>
        <result column="out_remark" jdbcType="VARCHAR" property="outRemark"/>
        <result column="out_pic" jdbcType="VARCHAR" property="outPic"/>
        <!--  产品退换库  -->
        <result column="saleOrderReturnId" jdbcType="BIGINT" property="saleOrderReturnId"/>
        <result column="return_date" jdbcType="DATE" property="returnDate"/>
        <result column="return_type" jdbcType="VARCHAR" property="returnType"/>
        <result column="return_status" jdbcType="VARCHAR" property="returnStatus"/>
        <result column="return_reason" jdbcType="VARCHAR" property="returnReason"/>
        <result column="return_pic" jdbcType="VARCHAR" property="returnPic"/>
        <result column="return_new_obj_id" jdbcType="BIGINT" property="returnNewObjId"/>
        <result column="return_new_obj_no" jdbcType="VARCHAR" property="returnNewObjNo"/>
    </resultMap>
    <resultMap id="SateSaleOrderVO" type="com.fawkes.project.example.domain.vo.StatSaleOrderVO">
        <result column="stat_month" jdbcType="INTEGER" property="month"/>
        <result column="payment_amount" jdbcType="INTEGER" property="paymentAmount"/>
        <result column="sale_amount" jdbcType="DECIMAL" property="saleAmount"/>
        <result column="client_type" jdbcType="VARCHAR" property="saleAmount"/>
    </resultMap>


    <select id="isExistedOrderNo" resultType="java.lang.Boolean">
        SELECT COUNT( * ) FROM sale_order
        WHERE delete_flag = 0 AND order_no = #{order.orderNo}
        <if test="order.id != null">AND id != #{order.id}</if>
    </select>

    <select id="getMaxOrderNoInYear" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(order_no)
        FROM sale_order
        WHERE YEAR (create_date) = #{searchYear}
            LIMIT 1
    </select>

    <select id="selectById" resultMap="SaleOrderDTO">
        SELECT a.*,
               b.client_name
        FROM sale_order a
                 LEFT JOIN client b ON b.id = a.client_id
        WHERE a.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByIds" resultMap="SaleOrderVO">
        SELECT
        a.id,
        a.order_no,
        a.order_name,
        IFNULL( SUM( b.sale_price ), 0 ) AS sale_price,
        IFNULL( SUM( b.sale_quantity ), 0 ) AS sale_quantity,
        a.sign_date,
        a.planned_delivery_date,
        a.liable_user_id,
        a.liable_user_name
        FROM
        sale_order a
        LEFT JOIN sale_order_goods b ON b.order_id = a.id
        AND b.delete_flag = 0
        WHERE
        a.delete_flag = 0 AND a.id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">#{orderId}</foreach>
        GROUP BY
        a.id
    </select>

    <select id="selectByClientIds" resultMap="SaleOrderVO">
        SELECT
        a.id,
        a.order_no,
        a.order_name,
        a.client_id,
        a.industry,
        IFNULL( SUM( b.sale_price ), 0 ) AS sale_price,
        IFNULL( SUM( b.sale_quantity ), 0 ) AS sale_quantity,
        a.sign_date,
        a.planned_delivery_date,
        a.liable_user_id,
        a.liable_user_name
        FROM
        sale_order a
        LEFT JOIN sale_order_goods b ON b.order_id = a.id
        AND b.delete_flag = 0
        WHERE
        a.delete_flag = 0 AND a.client_id IN
        <foreach collection="clientIds" item="clientId" open="(" separator="," close=")">#{clientId}</foreach>
        GROUP BY
        a.id
    </select>

    <select id="list" parameterType="com.fawkes.project.example.domain.param.SaleOrderPageParam"
            resultMap="SaleOrderVO">
        SELECT * FROM (
        SELECT
        IFNULL( SUM( c.sale_price ), 0 ) AS sale_price,
        a.id,
        a.create_by,
        a.create_date,
        a.update_by,
        a.update_date,
        a.delete_flag,
        a.order_no,
        a.order_name,
        a.sign_date,
        a.planned_delivery_date,
        a.actual_delivery_date,
        a.client_id,
        b.client_name,
        a.liable_user_id,
        a.liable_user_name,
        a.industry,
        a.project_type,
        IFNULL( a.payment_amount, 0 ) AS payment_amount,
        a.`status`,
        a.order_enclosure
        FROM
        sale_order a
        LEFT JOIN client b ON b.id = a.client_id AND b.delete_flag = 0
        LEFT JOIN sale_order_goods c ON c.order_id = a.id AND c.delete_flag = 0
        <where>
            a.delete_flag = 0
            <if test="param.orderNo != null and param.orderNo != ''">
                AND a.order_no LIKE CONCAT( '%', #{param.orderNo}, '%' )
            </if>
            <if test="param.orderName != null and param.orderName != ''">
                AND a.order_name LIKE CONCAT( '%', #{param.orderName}, '%' )
            </if>
            <if test="param.clientName != null and param.clientName != ''">
                AND b.client_name LIKE CONCAT( '%', #{param.clientName}, '%' )
            </if>
            <if test="param.liableUserId != null and param.liableUserId != ''">
                AND a.liable_user_id = #{param.liableUserId}
            </if>
            <if test="param.status != null and param.status != ''">
                AND a.`status` = #{param.status}
            </if>
            <if test="param.clientId != null">
                AND a.client_id = #{param.clientId}
            </if>
            <if test="param.signDateStart != null">
                AND a.sign_date <![CDATA[ >= ]]> #{param.signDateStart}
            </if>
            <if test="param.signDateEnd != null">
                AND a.sign_date <![CDATA[ <= ]]> #{param.signDateEnd}
            </if>
            <if test="param.searchYear != null">
                AND YEAR( a.sign_date ) = #{param.searchYear}
            </if>
        </where>
        GROUP BY
        a.id
        ) temp
        <where>
            <if test="param.salePriceMin != null">
                AND temp.sale_price <![CDATA[ >= ]]> #{param.salePriceMin}
            </if>
            <if test="param.salePriceMax != null">
                AND temp.sale_price <![CDATA[ <= ]]> #{param.salePriceMax}
            </if>
        </where>
        ORDER BY
        temp.create_date DESC
    </select>

    <select id="listAll" resultMap="SaleOrder">
        select *
        from sale_order
        where delete_flag = 0
    </select>

    <select id="getTotalSaleAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(b.sale_price), 0) AS sale_amount
        FROM sale_order a
                 LEFT JOIN sale_order_goods b ON b.order_id = a.id
        WHERE a.delete_flag = 0
          AND b.delete_flag = 0
    </select>

    <select id="selectReturnObjBySaleOrderReturnId" resultMap="SaleProductObjVO">
        SELECT a.id,
               a.object_no,
               a.craft_id,
               a.`status`,
               b.return_type,
               b.create_date AS return_date,
               b.return_status,
               b.return_reason,
               b.return_pic,
               b.return_new_obj_id,
               c.object_no   AS return_new_obj_no
        FROM product_object a
                 LEFT JOIN sale_order_return b ON b.obj_id = a.id
                 LEFT JOIN product_object c ON c.id = b.return_new_obj_id
        WHERE b.id = #{saleOrderReturnId}
    </select>

    <select id="selectReturnByObjIdAndOrderId" resultMap="SaleOrderReturn">
        SELECT *
        FROM sale_order_return
        WHERE delete_flag = 0
          AND obj_id = #{objId}
          AND order_id = #{orderId}
    </select>

    <select id="selectOutGoodsByCraftIds" resultMap="SaleOutGoodsVO">
        SELECT
        b.id AS product_id,
        b.`name` AS product_name,
        b.model AS product_model,
        a.id AS craft_id,
        a.craft_code AS craft_code
        FROM
        product_craft a
        LEFT JOIN product b ON b.id = a.product_id
        WHERE
        a.id
        IN
        <foreach close=")" collection="craftIds" item="craftId" open="(" separator=", ">
            #{craftId}
        </foreach>
    </select>

    <insert id="insert" parameterType="com.fawkes.project.example.common.model.SaleOrder">
        insert into sale_order (id, create_by, create_date,
                                update_by, update_date, delete_flag,
                                order_no, order_name, sign_date,
                                planned_delivery_date, client_id, liable_user_id,
                                liable_user_name, industry, project_type,
                                payment_amount, `status`, order_enclosure)
        values (#{id,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP},
                #{updateBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=INTEGER},
                #{orderNo,jdbcType=VARCHAR}, #{orderName,jdbcType=VARCHAR}, #{signDate,jdbcType=DATE},
                #{plannedDeliveryDate,jdbcType=DATE}, #{clientId,jdbcType=BIGINT}, #{liableUserId,jdbcType=BIGINT},
                #{liableUserName,jdbcType=VARCHAR}, #{industry,jdbcType=VARCHAR}, #{projectType,jdbcType=VARCHAR},
                #{paymentAmount,jdbcType=DECIMAL}, #{status,jdbcType=VARCHAR}, #{orderEnclosure,jdbcType=LONGVARCHAR})
    </insert>

    <insert id="returnBatch" parameterType="com.fawkes.project.example.common.model.SaleOrderReturn">
        insert into sale_order_return (id, create_by, create_date, update_by, update_date, delete_flag,
        order_id, obj_id, return_type, return_status, return_reason, return_pic,
        return_new_obj_id)
        VALUES
        <foreach collection="returnList" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR}, #{item.createDate,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP},
            #{item.deleteFlag,jdbcType=INTEGER},
            #{item.orderId,jdbcType=BIGINT}, #{item.objId,jdbcType=BIGINT}, #{item.returnType,jdbcType=VARCHAR},
            #{item.returnStatus,jdbcType=VARCHAR}, #{item.returnReason,jdbcType=VARCHAR},
            #{item.returnPic,jdbcType=LONGVARCHAR},
            #{item.returnNewObjId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="outBatch">
        <foreach collection="objIds" item="id" separator=";">
            update product_object
            set sale_order_id = #{saleOrderId,jdbcType=BIGINT},
            out_date = #{outDate,jdbcType=TIMESTAMP},
            out_user_id = #{outUserId,jdbcType=BIGINT},
            out_user_name = #{outUserName,jdbcType=VARCHAR},
            out_remark = #{outRemark,jdbcType=VARCHAR},
            out_pic = #{outPic,jdbcType=VARCHAR}
            where id = #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateSaleOrderIdByIds">
        <foreach collection="objIds" item="id" separator=";">
            update product_object
            set
            sale_order_id = #{saleOrderId},
            out_date = #{updateInfo.updateDate},
            out_user_id = #{updateInfo.id},
            out_user_name = #{updateInfo.updateBy}
            where id = #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateBatchUnqualifiedObj" parameterType="com.fawkes.project.example.domain.param.SaleReturnObj">
        <foreach collection="objList" item="item" separator=";">
            update product_object
            set
            `status` = 2,
            unqualified_reason = #{item.returnReason},
            unqualified_pic = #{item.returnPic}
            where id = #{item.objectId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="deleteReturn">
        update sale_order_return
        set delete_flag = -1,
            update_date = #{updateInfo.updateDate},
            update_by   = #{updateInfo.updateBy}
        where id = #{id}
    </update>

    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fawkes.project.example.common.model.SaleOrder">
        update sale_order
        set create_by             = #{createBy,jdbcType=VARCHAR},
            create_date           = #{createDate,jdbcType=TIMESTAMP},
            update_by             = #{updateBy,jdbcType=VARCHAR},
            update_date           = #{updateDate,jdbcType=TIMESTAMP},
            delete_flag           = #{deleteFlag,jdbcType=INTEGER},
            order_no              = #{orderNo,jdbcType=VARCHAR},
            order_name            = #{orderName,jdbcType=VARCHAR},
            sign_date             = #{signDate,jdbcType=DATE},
            planned_delivery_date = #{plannedDeliveryDate,jdbcType=DATE},
            client_id             = #{clientId,jdbcType=BIGINT},
            liable_user_id        = #{liableUserId,jdbcType=BIGINT},
            liable_user_name      = #{liableUserName,jdbcType=VARCHAR},
            industry              = #{industry,jdbcType=VARCHAR},
            project_type          = #{projectType,jdbcType=VARCHAR},
            payment_amount        = #{paymentAmount,jdbcType=DECIMAL},
            status                = #{status,jdbcType=CHAR},
            order_enclosure       = #{orderEnclosure,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="deleteByIds">
        update sale_order
        set delete_flag = -1,
        update_by = #{updateInfo.updateBy,jdbcType=VARCHAR},
        update_date = #{updateInfo.updateDate,jdbcType=TIMESTAMP}
        where id IN
        <foreach close=")" collection="ids" item="id" open="(" separator=", ">#{id}</foreach>
    </update>

    <select id="selectCraftDetail" resultMap="SaleCraftDetailVO">
        SELECT a.id,
               a.order_id,
               a.craft_id,
               a.sale_quantity - COUNT(d.id) AS required_quantity,
               c.`name`                      AS product_name,
               c.model                       AS product_model
        FROM sale_order_goods a
                 LEFT JOIN product_craft b ON b.id = a.craft_id
                 LEFT JOIN product c ON c.id = b.product_id
                 LEFT JOIN product_object d ON d.sale_order_id = a.order_id
            AND d.craft_id = a.craft_id
            AND d.out_date IS NOT NULL
            AND d.delete_flag = 0
        WHERE a.delete_flag = 0
          AND a.order_id = #{orderId}
          AND a.craft_id = #{craftId}
        GROUP BY a.id LIMIT 1
    </select>

    <select id="selectAvailableObj" resultMap="SaleProductObjVO">
        SELECT a.id,
               a.object_no,
               b.id AS craft_id,
               b.craft_code
        FROM product_object a
                 LEFT JOIN product_craft b ON b.id = a.craft_id
        WHERE a.delete_flag = 0
          AND a.`status` = 1
          AND ISNULL(a.sale_order_id)
          AND ISNULL(a.use_order_id)
          AND a.craft_id = #{craftId}
    </select>

    <select id="selectOutObjByOrderId" resultMap="SaleProductObjVO">
        SELECT a.id,
               a.object_no,
               a.sale_order_id,
               a.out_date,
               a.out_user_id,
               a.out_user_name,
               a.out_remark,
               a.out_pic,
               b.id AS craft_id,
               b.craft_code
        FROM product_object a
                 LEFT JOIN product_craft b ON b.id = a.craft_id
        WHERE a.delete_flag = 0
          AND ISNULL(a.return_date)
          AND a.sale_order_id = #{orderId}
    </select>

    <select id="selectOutObjByOrderIds" resultMap="SaleProductObjVO">
        SELECT a.id,
        a.object_no,
        a.sale_order_id,
        a.out_date,
        a.out_user_id,
        a.out_user_name,
        a.out_remark,
        a.out_pic,
        b.id AS craft_id,
        b.craft_code
        FROM product_object a
        LEFT JOIN product_craft b ON b.id = a.craft_id
        WHERE a.delete_flag = 0
        AND ISNULL(a.return_date)
        AND a.sale_order_id IN
        <foreach close=")" collection="orderIds" item="orderId" open="(" separator=", ">#{orderId}</foreach>
    </select>

    <select id="selectReturnObjByOrderId" resultMap="SaleProductObjVO">
        SELECT b.id,
               b.object_no,
               a.id          AS saleOrderReturnId,
               a.create_date AS return_date,
               a.return_type,
               a.return_status,
               a.return_reason,
               a.return_pic,
               a.return_new_obj_id,
               d.object_no   AS return_new_obj_no,
               c.id          AS craft_id,
               c.craft_code
        FROM sale_order_return a
                 LEFT JOIN product_object b ON a.obj_id = b.id
                 LEFT JOIN product_craft c ON c.id = b.craft_id
                 LEFT JOIN product_object d ON d.id = a.return_new_obj_id
        WHERE a.delete_flag = 0
          AND a.order_id = #{orderId}
    </select>

    <select id="selectReturnObjByOrderIds" resultMap="SaleProductObjVO">
        SELECT
        b.id,
        b.object_no,
        a.id AS saleOrderReturnId,
        a.order_id AS sale_order_id,
        a.create_date AS return_date,
        a.return_type,
        a.return_status,
        a.return_reason,
        a.return_pic,
        a.return_new_obj_id,
        d.object_no AS return_new_obj_no,
        c.id AS craft_id,
        c.craft_code
        FROM
        sale_order_return a
        LEFT JOIN product_object b ON a.obj_id = b.id
        LEFT JOIN product_craft c ON c.id = b.craft_id
        LEFT JOIN product_object d ON d.id = a.return_new_obj_id
        WHERE
        a.delete_flag = 0
        AND a.order_id IN
        <foreach close=")" collection="orderIds" item="orderId" open="(" separator=", ">#{orderId}</foreach>
    </select>

    <select id="listByYear" resultMap="SaleOrderVO">
        SELECT a.id,
               a.sign_date,
               a.industry,
               IFNULL(a.payment_amount, 0)  AS payment_amount,
               b.client_type,
               b.client_name,
               b.province                   AS client_province,
               IFNULL(SUM(c.sale_price), 0) AS sale_price
        FROM sale_order a
                 LEFT JOIN client b ON b.id = a.client_id
                 LEFT JOIN sale_order_goods c ON c.order_id = a.id
            AND c.delete_flag = 0
        WHERE a.delete_flag = 0
                  AND YEAR ( a.sign_date ) = #{searchYear}
        GROUP BY
            a.id
    </select>

    <select id="listByYearAndProvince" resultMap="SaleOrderVO">
        SELECT a.id,
        a.sign_date,
        a.industry,
        IFNULL(a.payment_amount, 0) AS payment_amount,
        b.client_type,
        b.client_name,
        b.province AS client_province,
        IFNULL(SUM(c.sale_price), 0) AS sale_price
        FROM sale_order a
        LEFT JOIN client b ON b.id = a.client_id
        LEFT JOIN sale_order_goods c ON c.order_id = a.id
        AND c.delete_flag = 0
        WHERE a.delete_flag = 0
        <if test="searchYear != null">
            AND YEAR ( a.sign_date ) = #{searchYear}
        </if>
        <if test="province != null and province != ''">
            AND b.province = #{province}
        </if>
        GROUP BY
        a.id
    </select>

    <update id="cleanOutByObjIds">
        <foreach collection="objIds" item="id" separator=";">
            update product_object
            set sale_order_id = null,
            out_date = null,
            out_user_id = null,
            out_user_name = null,
            out_remark = null,
            out_pic = null,
            return_type = null,
            return_date = null,
            return_status = null,
            return_reason = null,
            return_pic = null,
            return_new_obj_id = null
            where id = #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="cleanOutBySaleOrderIds">
        <foreach collection="saleOrderIds" item="saleOrderId" separator=";">
            update product_object
            set sale_order_id = null,
            out_date = null,
            out_user_id = null,
            out_user_name = null,
            out_remark = null,
            out_pic = null
            where sale_order_id = #{saleOrderId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateStatus">
        update sale_order
        set status               = #{status},
            actual_delivery_date = #{actualDeliveryDate}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectProductObjectById" resultMap="SaleProductObjVO">
        select a.id,
               a.object_no,
               a.craft_id,
               a.`status`,
               a.use_order_id,
               a.sale_order_id,
               a.out_date,
               a.out_user_id,
               a.out_user_name,
               a.out_remark,
               a.out_pic,
               a.return_type,
               a.return_date,
               a.return_status,
               a.return_reason,
               a.return_pic,
               a.return_new_obj_id,
               b.object_no as return_new_obj_no
        from product_object a
                 left join product_object b on b.id = a.return_new_obj_id
        where a.id = #{objId,jdbcType=BIGINT} limit 1
    </select>

    <update id="updateProductObjByPrimaryKey" parameterType="com.fawkes.project.example.domain.vo.SaleProductObjVO">
        update product_object
        set `status`          = #{obj.status},
            out_date          = #{obj.outDate},
            out_user_id       = #{obj.outUserId},
            out_user_name     = #{obj.outUserName},
            out_remark        = #{obj.outRemark},
            out_pic           = #{obj.outPic},
            return_status     = #{obj.returnStatus},
            return_reason     = #{obj.returnReason},
            return_new_obj_id = #{obj.returnNewObjId}
        where id = #{obj.id,jdbcType=BIGINT}
    </update>

    <select id="listProductObjectBySaleOrderIds" resultMap="SaleProductObjVO">
        SELECT
        a.id,
        a.object_no,
        a.sale_order_id,
        a.out_date,
        a.out_user_id,
        a.out_user_name,
        a.out_remark,
        a.out_pic,
        a.return_type,
        a.return_date,
        a.return_status,
        a.return_reason,
        a.return_pic,
        a.return_new_obj_id,
        a.craft_id
        FROM
        product_object a
        WHERE
        a.delete_flag = 0
        and a.sale_order_id
        IN
        <foreach close=")" collection="saleOrderIds" item="id" open="(" separator=", ">
            #{id}
        </foreach>
    </select>

    <select id="statSaleOrderByYear" resultMap="SateSaleOrderVO">
        <![CDATA[
        WITH RECURSIVE months AS (SELECT 1 AS stat_month
                                  UNION ALL
                                  SELECT stat_month + 1
                                  FROM months
                                  WHERE stat_month < 12) ]]>
        SELECT a.stat_month,
               IFNULL(SUM(c.sale_price), 0)     AS sale_amount,
               IFNULL(SUM(b.payment_amount), 0) AS payment_amount
        FROM months a
                 LEFT JOIN sale_order b ON MONTH ( b.sign_date ) = a.stat_month
            AND b.delete_flag = 0
            AND YEAR ( b.sign_date ) = #{searYear}
            LEFT JOIN sale_order_goods c
        ON c.order_id = b.id
            AND c.delete_flag = 0
        GROUP BY
            a.stat_month
    </select>

    <select id="selectBySignDate" resultMap="SaleOrder">
        select * from sale_order
        <where>
            delete_flag = 0
            <if test="signYear != null and signYear != ''">
                and YEAR ( sign_date ) = #{signYear}
            </if>
        </where>
    </select>
</mapper>